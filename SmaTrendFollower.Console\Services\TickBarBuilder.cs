using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using System.Collections.Concurrent;
using System.Text.Json;
using StackExchange.Redis;

namespace SmaTrendFollower.Services;

/// <summary>
/// Production-ready tick bar builder service for reconstructing bars from tick data
/// Supports Renko, Range, Volume, and Time-based bars for alternate signal engines
/// </summary>
public class TickBarBuilder : ITickBarBuilder
{
    private readonly ILogger<TickBarBuilder> _logger;
    private readonly IOptimizedRedisConnectionService _redisService;
    private readonly ITimeProvider _timeProvider;
    private readonly ITickStreamService _tickStreamService;

    private BarBuildingStatus _status;
    private readonly ConcurrentDictionary<string, ConcurrentDictionary<CustomBarType, BarBuildingConfig>> _configurations;
    private readonly ConcurrentDictionary<string, ConcurrentDictionary<CustomBarType, CustomBar>> _currentBars;
    private readonly ConcurrentDictionary<string, BarBuildingStatistics> _statistics;
    private readonly ConcurrentDictionary<string, byte> _processedSymbols; // Using as concurrent set
    private readonly SemaphoreSlim _operationLock;
    private readonly Timer _maintenanceTimer;
    private bool _disposed;

    // Redis key patterns
    private const string CompletedBarsKeyPattern = "bars:completed:{0}:{1}"; // symbol:bartype
    private const string CurrentBarKeyPattern = "bars:current:{0}:{1}"; // symbol:bartype
    private const string StatisticsKeyPattern = "bars:stats:{0}"; // symbol
    private const string ConfigKeyPattern = "bars:config:{0}:{1}"; // symbol:bartype

    // === Events ===
    
    public event EventHandler<CustomBarCompletedEventArgs>? BarCompleted;
    public event EventHandler<BarBuilderConfigUpdatedEventArgs>? ConfigurationUpdated;

#pragma warning disable CS0067 // Event is never used - reserved for future implementation
    public event EventHandler<BarBuilderErrorEventArgs>? ErrorOccurred;
#pragma warning restore CS0067

    public TickBarBuilder(
        ILogger<TickBarBuilder> logger,
        IOptimizedRedisConnectionService redisService,
        ITimeProvider timeProvider,
        ITickStreamService tickStreamService)
    {
        _logger = logger;
        _redisService = redisService;
        _timeProvider = timeProvider;
        _tickStreamService = tickStreamService;

        _status = BarBuildingStatus.Stopped;
        _configurations = new ConcurrentDictionary<string, ConcurrentDictionary<CustomBarType, BarBuildingConfig>>();
        _currentBars = new ConcurrentDictionary<string, ConcurrentDictionary<CustomBarType, CustomBar>>();
        _statistics = new ConcurrentDictionary<string, BarBuildingStatistics>();
        _processedSymbols = new ConcurrentDictionary<string, byte>(); // Using as concurrent set
        _operationLock = new SemaphoreSlim(1, 1);

        // Maintenance timer for forced bar completions and cleanup
        _maintenanceTimer = new Timer(PerformMaintenance, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
    }

    // === Core Methods ===

    public async Task StartBuildingAsync(IEnumerable<string> symbols, IEnumerable<BarBuildingConfig> configs, CancellationToken cancellationToken = default)
    {
        await _operationLock.WaitAsync(cancellationToken);
        try
        {
            _status = BarBuildingStatus.Starting;
            var symbolList = symbols.ToList();
            var configList = configs.ToList();
            
            _logger.LogInformation("Starting bar building for {SymbolCount} symbols with {ConfigCount} configurations",
                symbolList.Count, configList.Count);

            // Subscribe to tick stream events
            _tickStreamService.TradeReceived += OnTradeReceived;
            _tickStreamService.QuoteReceived += OnQuoteReceived;

            // Initialize configurations and current bars
            foreach (var symbol in symbolList)
            {
                _processedSymbols.TryAdd(symbol, 0); // Add to concurrent set
                _configurations[symbol] = new ConcurrentDictionary<CustomBarType, BarBuildingConfig>();
                _currentBars[symbol] = new ConcurrentDictionary<CustomBarType, CustomBar>();
                
                // Initialize statistics
                _statistics[symbol] = new BarBuildingStatistics(
                    Symbol: symbol,
                    TotalBarsBuilt: 0,
                    ActiveBarTypes: 0,
                    LastBarTime: DateTime.MinValue,
                    LastTickProcessed: DateTime.MinValue,
                    TotalTicksProcessed: 0,
                    TotalVolumeProcessed: 0,
                    AverageBarDuration: 0,
                    BarCountsByType: new Dictionary<CustomBarType, int>(),
                    StatisticsTime: _timeProvider.UtcNow
                );

                // Apply configurations for this symbol
                foreach (var config in configList)
                {
                    _configurations[symbol][config.BarType] = config;
                    await LoadCurrentBarAsync(symbol, config.BarType);
                }
            }

            _status = BarBuildingStatus.Active;
            _logger.LogInformation("Bar building started for {Count} symbols", symbolList.Count);
        }
        catch (Exception ex)
        {
            _status = BarBuildingStatus.Error;
            _logger.LogError(ex, "Failed to start bar building");
            throw;
        }
        finally
        {
            _operationLock.Release();
        }
    }

    public async Task StopBuildingAsync(CancellationToken cancellationToken = default)
    {
        await _operationLock.WaitAsync(cancellationToken);
        try
        {
            _logger.LogInformation("Stopping bar building");
            
            // Unsubscribe from tick stream events
            _tickStreamService.TradeReceived -= OnTradeReceived;
            _tickStreamService.QuoteReceived -= OnQuoteReceived;

            // Save all current bars
            foreach (var symbol in _processedSymbols.Keys)
            {
                if (_currentBars.TryGetValue(symbol, out var symbolBars))
                {
                    foreach (var kvp in symbolBars)
                    {
                        await SaveCurrentBarAsync(symbol, kvp.Key, kvp.Value);
                    }
                }
            }

            _processedSymbols.Clear();
            _configurations.Clear();
            _currentBars.Clear();
            _statistics.Clear();
            _status = BarBuildingStatus.Stopped;
            
            _logger.LogInformation("Bar building stopped");
        }
        catch (Exception ex)
        {
            _status = BarBuildingStatus.Error;
            _logger.LogError(ex, "Failed to stop bar building");
            throw;
        }
        finally
        {
            _operationLock.Release();
        }
    }

    public async Task ProcessTradeTickAsync(TradeTick tick)
    {
        if (_status != BarBuildingStatus.Active || !_processedSymbols.ContainsKey(tick.Symbol))
            return;

        try
        {
            if (!_configurations.TryGetValue(tick.Symbol, out var symbolConfigs) ||
                !_currentBars.TryGetValue(tick.Symbol, out var symbolBars))
                return;

            // Process tick for each configured bar type
            foreach (var config in symbolConfigs.Values)
            {
                await ProcessTickForBarType(tick, config, symbolBars);
            }

            // Update statistics
            await UpdateStatisticsAsync(tick.Symbol, tick);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process trade tick for {Symbol}", tick.Symbol);
            
            ErrorOccurred?.Invoke(this, new BarBuilderErrorEventArgs
            {
                Symbol = tick.Symbol,
                BarType = CustomBarType.Tick, // Default
                Error = ex,
                ErrorTime = _timeProvider.UtcNow,
                Context = "ProcessTradeTickAsync"
            });
        }
    }

    public async Task ProcessQuoteTickAsync(QuoteTick tick)
    {
        if (_status != BarBuildingStatus.Active || !_processedSymbols.ContainsKey(tick.Symbol))
            return;

        try
        {
            // Update bid/ask analysis for current bars if enabled
            if (_currentBars.TryGetValue(tick.Symbol, out var symbolBars))
            {
                foreach (var kvp in symbolBars.ToList())
                {
                    var config = _configurations[tick.Symbol][kvp.Key];
                    if (config.EnableBidAskAnalysis)
                    {
                        await UpdateBidAskAnalysis(tick, kvp.Value);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process quote tick for {Symbol}", tick.Symbol);
        }
    }

    public async Task<CustomBar?> GetCurrentBarAsync(string symbol, CustomBarType barType)
    {
        if (_currentBars.TryGetValue(symbol, out var symbolBars) &&
            symbolBars.TryGetValue(barType, out var currentBar))
        {
            return currentBar;
        }

        // Try loading from Redis
        return await LoadCurrentBarAsync(symbol, barType);
    }

    public async Task<IEnumerable<CustomBar>> GetCompletedBarsAsync(string symbol, CustomBarType barType, int count = 100)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(CompletedBarsKeyPattern, symbol, barType);
            var bars = await database.ListRangeAsync(key, 0, count - 1);
            
            return bars
                .Select(b => JsonSerializer.Deserialize<CustomBar>(b!))
                .OrderByDescending(b => b.EndTime);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get completed bars for {Symbol}:{BarType}", symbol, barType);
            return Enumerable.Empty<CustomBar>();
        }
    }

    public async Task<BarBuildingStatistics?> GetBuildingStatisticsAsync(string symbol)
    {
        if (_statistics.TryGetValue(symbol, out var stats))
            return stats;

        // Try loading from Redis
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(StatisticsKeyPattern, symbol);
            var json = await database.StringGetAsync(key);
            
            if (json.HasValue)
            {
                var loadedStats = JsonSerializer.Deserialize<BarBuildingStatistics>(json!);
                _statistics[symbol] = loadedStats;
                return loadedStats;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load statistics for {Symbol}", symbol);
        }

        return null;
    }

    public async Task ForceBarCompletionAsync(string symbol, CustomBarType barType, string reason = "Manual")
    {
        if (!_currentBars.TryGetValue(symbol, out var symbolBars) ||
            !symbolBars.TryGetValue(barType, out var currentBar))
            return;

        try
        {
            var completedBar = currentBar with 
            { 
                IsComplete = true,
                EndTime = _timeProvider.UtcNow,
                Metadata = currentBar.Metadata with { CompletionReason = reason }
            };

            await CompleteBarAsync(symbol, barType, completedBar, BarCompletionTrigger.Manual);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to force bar completion for {Symbol}:{BarType}", symbol, barType);
        }
    }

    public async Task ResetBarsAsync(string symbol, CustomBarType? barType = null)
    {
        if (!_currentBars.TryGetValue(symbol, out var symbolBars))
            return;

        try
        {
            if (barType.HasValue)
            {
                // Reset specific bar type
                symbolBars.TryRemove(barType.Value, out _);
                
                var database = await _redisService.GetDatabaseAsync();
                var key = string.Format(CurrentBarKeyPattern, symbol, barType.Value);
                await database.KeyDeleteAsync(key);
            }
            else
            {
                // Reset all bar types for symbol
                symbolBars.Clear();
                
                var database = await _redisService.GetDatabaseAsync();
                foreach (var type in Enum.GetValues<CustomBarType>())
                {
                    var key = string.Format(CurrentBarKeyPattern, symbol, type);
                    await database.KeyDeleteAsync(key);
                }
            }

            _logger.LogInformation("Reset bars for {Symbol}:{BarType}", symbol, barType?.ToString() ?? "All");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to reset bars for {Symbol}:{BarType}", symbol, barType);
        }
    }

    // === Configuration ===

    public async Task UpdateConfigurationAsync(string symbol, BarBuildingConfig config)
    {
        if (!_configurations.TryGetValue(symbol, out var symbolConfigs))
        {
            symbolConfigs = new ConcurrentDictionary<CustomBarType, BarBuildingConfig>();
            _configurations[symbol] = symbolConfigs;
        }

        var oldConfig = symbolConfigs.GetValueOrDefault(config.BarType);
        symbolConfigs[config.BarType] = config;

        // Save to Redis
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(ConfigKeyPattern, symbol, config.BarType);
            var json = JsonSerializer.Serialize(config);
            await database.StringSetAsync(key, json, TimeSpan.FromDays(30));

            if (oldConfig != null)
            {
                ConfigurationUpdated?.Invoke(this, new BarBuilderConfigUpdatedEventArgs
                {
                    Symbol = symbol,
                    OldConfig = oldConfig,
                    NewConfig = config,
                    UpdateTime = _timeProvider.UtcNow
                });
            }

            _logger.LogInformation("Updated configuration for {Symbol}:{BarType}", symbol, config.BarType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update configuration for {Symbol}:{BarType}", symbol, config.BarType);
        }
    }

    public async Task<BarBuildingConfig?> GetConfigurationAsync(string symbol, CustomBarType barType)
    {
        if (_configurations.TryGetValue(symbol, out var symbolConfigs) &&
            symbolConfigs.TryGetValue(barType, out var config))
        {
            return config;
        }

        // Try loading from Redis
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(ConfigKeyPattern, symbol, barType);
            var json = await database.StringGetAsync(key);
            
            if (json.HasValue)
            {
                var loadedConfig = JsonSerializer.Deserialize<BarBuildingConfig>(json!);
                
                if (!_configurations.ContainsKey(symbol))
                    _configurations[symbol] = new ConcurrentDictionary<CustomBarType, BarBuildingConfig>();
                    
                _configurations[symbol][barType] = loadedConfig;
                return loadedConfig;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load configuration for {Symbol}:{BarType}", symbol, barType);
        }

        return null;
    }

    public BarBuildingStatus GetStatus() => _status;

    public IEnumerable<string> GetProcessedSymbols() => _processedSymbols.Keys;

    public IEnumerable<CustomBarType> GetSupportedBarTypes() => Enum.GetValues<CustomBarType>();

    // === Private Helper Methods ===

    private async void OnTradeReceived(object? sender, TradeTickEventArgs e)
    {
        await ProcessTradeTickAsync(e.TradeTick);
    }

    private async void OnQuoteReceived(object? sender, QuoteTickEventArgs e)
    {
        await ProcessQuoteTickAsync(e.QuoteTick);
    }

    private async Task ProcessTickForBarType(TradeTick tick, BarBuildingConfig config, ConcurrentDictionary<CustomBarType, CustomBar> symbolBars)
    {
        var currentBar = symbolBars.GetOrAdd(config.BarType, _ => CreateNewBar(tick.Symbol, config, tick));
        
        // Update bar with tick data
        var updatedBar = UpdateBarWithTick(currentBar, tick, config);
        symbolBars[config.BarType] = updatedBar;

        // Check if bar should be completed
        if (ShouldCompleteBar(updatedBar, config, tick))
        {
            var completedBar = updatedBar with 
            { 
                IsComplete = true,
                EndTime = tick.Timestamp,
                Metadata = updatedBar.Metadata with { CompletionReason = "Parameter" }
            };

            await CompleteBarAsync(tick.Symbol, config.BarType, completedBar, BarCompletionTrigger.Parameter);
            
            // Start new bar
            symbolBars[config.BarType] = CreateNewBar(tick.Symbol, config, tick);
        }
        else
        {
            // Save current bar state
            await SaveCurrentBarAsync(tick.Symbol, config.BarType, updatedBar);
        }
    }

    private CustomBar CreateNewBar(string symbol, BarBuildingConfig config, TradeTick tick)
    {
        return new CustomBar(
            Symbol: symbol,
            BarType: config.BarType,
            Open: tick.Price,
            High: tick.Price,
            Low: tick.Price,
            Close: tick.Price,
            Volume: tick.Size,
            DollarVolume: tick.Price * tick.Size,
            TickCount: 1,
            StartTime: tick.Timestamp,
            EndTime: tick.Timestamp,
            IsComplete: false,
            Parameter: config.Parameter,
            Metadata: new BarMetadata(
                BidVolumeRatio: 0,
                AskVolumeRatio: 0,
                AverageSpread: 0,
                VWAP: tick.Price,
                UptickCount: 0,
                DowntickCount: 0,
                MaxSpread: 0,
                MinSpread: 0,
                CompletionReason: ""
            )
        );
    }

    private CustomBar UpdateBarWithTick(CustomBar bar, TradeTick tick, BarBuildingConfig config)
    {
        var newHigh = Math.Max(bar.High, tick.Price);
        var newLow = Math.Min(bar.Low, tick.Price);
        var newVolume = bar.Volume + tick.Size;
        var newDollarVolume = bar.DollarVolume + (tick.Price * tick.Size);
        var newTickCount = bar.TickCount + 1;
        
        // Calculate VWAP
        var newVWAP = newDollarVolume / newVolume;
        
        // Determine tick direction
        var tickDirection = tick.Price > bar.Close ? BarTickDirection.Uptick :
                           tick.Price < bar.Close ? BarTickDirection.Downtick : BarTickDirection.Zero;

        var newUptickCount = bar.Metadata.UptickCount + (tickDirection == BarTickDirection.Uptick ? 1 : 0);
        var newDowntickCount = bar.Metadata.DowntickCount + (tickDirection == BarTickDirection.Downtick ? 1 : 0);

        return bar with
        {
            High = newHigh,
            Low = newLow,
            Close = tick.Price,
            Volume = newVolume,
            DollarVolume = newDollarVolume,
            TickCount = newTickCount,
            EndTime = tick.Timestamp,
            Metadata = bar.Metadata with
            {
                VWAP = newVWAP,
                UptickCount = newUptickCount,
                DowntickCount = newDowntickCount
            }
        };
    }

    private bool ShouldCompleteBar(CustomBar bar, BarBuildingConfig config, TradeTick tick)
    {
        return config.BarType switch
        {
            CustomBarType.Renko => ShouldCompleteRenkoBar(bar, config),
            CustomBarType.Range => ShouldCompleteRangeBar(bar, config),
            CustomBarType.Volume => bar.Volume >= (long)config.Parameter,
            CustomBarType.Tick => bar.TickCount >= (int)config.Parameter,
            CustomBarType.Dollar => bar.DollarVolume >= config.Parameter,
            CustomBarType.Time => (tick.Timestamp - bar.StartTime).TotalMinutes >= (double)config.Parameter,
            _ => false
        };
    }

    private bool ShouldCompleteRenkoBar(CustomBar bar, BarBuildingConfig config)
    {
        var brickSize = config.Parameter;
        var priceMove = Math.Abs(bar.Close - bar.Open);
        return priceMove >= brickSize;
    }

    private bool ShouldCompleteRangeBar(CustomBar bar, BarBuildingConfig config)
    {
        var rangeSize = config.Parameter;
        var currentRange = bar.High - bar.Low;
        return currentRange >= rangeSize;
    }

    private async Task CompleteBarAsync(string symbol, CustomBarType barType, CustomBar completedBar, BarCompletionTrigger trigger)
    {
        try
        {
            // Save completed bar to Redis
            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(CompletedBarsKeyPattern, symbol, barType);
            var json = JsonSerializer.Serialize(completedBar);
            
            await database.ListLeftPushAsync(key, json);
            await database.ListTrimAsync(key, 0, 999); // Keep last 1000 bars
            await database.KeyExpireAsync(key, TimeSpan.FromDays(30));

            // Remove current bar
            var currentKey = string.Format(CurrentBarKeyPattern, symbol, barType);
            await database.KeyDeleteAsync(currentKey);

            // Fire event
            BarCompleted?.Invoke(this, new CustomBarCompletedEventArgs
            {
                Symbol = symbol,
                CompletedBar = completedBar,
                CompletionTime = _timeProvider.UtcNow,
                CompletionReason = trigger.ToString()
            });

            _logger.LogDebug("Completed {BarType} bar for {Symbol}: {Open}-{High}-{Low}-{Close} Volume: {Volume}",
                barType, symbol, completedBar.Open, completedBar.High, completedBar.Low, completedBar.Close, completedBar.Volume);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to complete bar for {Symbol}:{BarType}", symbol, barType);
        }
    }

    private async Task<CustomBar?> LoadCurrentBarAsync(string symbol, CustomBarType barType)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(CurrentBarKeyPattern, symbol, barType);
            var json = await database.StringGetAsync(key);
            
            if (json.HasValue)
            {
                var bar = JsonSerializer.Deserialize<CustomBar>(json!);
                
                if (!_currentBars.ContainsKey(symbol))
                    _currentBars[symbol] = new ConcurrentDictionary<CustomBarType, CustomBar>();
                    
                _currentBars[symbol][barType] = bar;
                return bar;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load current bar for {Symbol}:{BarType}", symbol, barType);
        }

        return null;
    }

    private async Task SaveCurrentBarAsync(string symbol, CustomBarType barType, CustomBar bar)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(CurrentBarKeyPattern, symbol, barType);
            var json = JsonSerializer.Serialize(bar);
            await database.StringSetAsync(key, json, TimeSpan.FromHours(24));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save current bar for {Symbol}:{BarType}", symbol, barType);
        }
    }

    private async Task UpdateBidAskAnalysis(QuoteTick quote, CustomBar bar)
    {
        // Update bid/ask analysis in bar metadata
        // This is a simplified implementation
        var spread = quote.AskPrice - quote.BidPrice;
        var newMaxSpread = Math.Max(bar.Metadata.MaxSpread, spread);
        var newMinSpread = bar.Metadata.MinSpread == 0 ? spread : Math.Min(bar.Metadata.MinSpread, spread);

        // This would need more sophisticated logic to track bid/ask volume ratios
        // For now, just update spread information
        await Task.CompletedTask;
    }

    private async Task UpdateStatisticsAsync(string symbol, TradeTick tick)
    {
        if (!_statistics.TryGetValue(symbol, out var stats))
            return;

        var updatedStats = stats with
        {
            LastTickProcessed = tick.Timestamp,
            TotalTicksProcessed = stats.TotalTicksProcessed + 1,
            TotalVolumeProcessed = stats.TotalVolumeProcessed + tick.Size,
            StatisticsTime = _timeProvider.UtcNow
        };

        _statistics[symbol] = updatedStats;

        // Periodically save to Redis
        if (updatedStats.TotalTicksProcessed % 100 == 0)
        {
            try
            {
                var database = await _redisService.GetDatabaseAsync();
                var key = string.Format(StatisticsKeyPattern, symbol);
                var json = JsonSerializer.Serialize(updatedStats);
                await database.StringSetAsync(key, json, TimeSpan.FromHours(24));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to save statistics for {Symbol}", symbol);
            }
        }
    }

    private void PerformMaintenance(object? state)
    {
        // Check for bars that need forced completion due to time limits
        _ = Task.Run(async () =>
        {
            try
            {
                var now = _timeProvider.UtcNow;
                
                foreach (var symbol in _processedSymbols.Keys)
                {
                    if (!_currentBars.TryGetValue(symbol, out var symbolBars) ||
                        !_configurations.TryGetValue(symbol, out var symbolConfigs))
                        continue;

                    foreach (var kvp in symbolBars.ToList())
                    {
                        var bar = kvp.Value;
                        var config = symbolConfigs[kvp.Key];
                        
                        if (!bar.IsComplete && (now - bar.StartTime) > config.MaxBarDuration)
                        {
                            await ForceBarCompletionAsync(symbol, kvp.Key, "MaxDuration");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during maintenance");
            }
        });
    }

    public void Dispose()
    {
        if (_disposed) return;
        
        _maintenanceTimer?.Dispose();
        _operationLock?.Dispose();
        _disposed = true;
    }
}
