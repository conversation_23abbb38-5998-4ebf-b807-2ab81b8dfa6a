using Alpaca.Markets;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using SmaTrendFollower.Extensions;
using SmaTrendFollower.Tests.TestHelpers;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Xunit;

namespace SmaTrendFollower.Tests.Integration;

[TestTimeout(TestTimeouts.Integration)]
[Trait("Category", TestCategories.Integration)]
public class Phase6IntegrationTests : IDisposable
{
    private readonly ServiceProvider _serviceProvider;
    private readonly Mock<IMarketDataService> _mockMarketDataService;
    private readonly Mock<IOptimizedRedisConnectionService> _mockRedisService;
    private readonly Mock<IDatabase> _mockRedis;
    private readonly Mock<ITickStreamService> _mockTickStreamService;
    private readonly Mock<IDynamicUniverseProvider> _mockUniverseProvider;
    private readonly Mock<IVixFallbackService> _mockVixFallbackService;
    private readonly Mock<IPolygonClientFactory> _mockPolygonFactory;
    private readonly Mock<IAlpacaClientFactory> _mockAlpacaFactory;
    private readonly Mock<IVWAPMonitorService> _mockVwapMonitorService;

    public Phase6IntegrationTests()
    {
        // Create mocks
        _mockMarketDataService = new Mock<IMarketDataService>();
        _mockRedisService = new Mock<IOptimizedRedisConnectionService>();
        _mockRedis = new Mock<IDatabase>();
        _mockTickStreamService = new Mock<ITickStreamService>();
        _mockUniverseProvider = new Mock<IDynamicUniverseProvider>();
        _mockVixFallbackService = new Mock<IVixFallbackService>();
        _mockPolygonFactory = new Mock<IPolygonClientFactory>();
        _mockAlpacaFactory = new Mock<IAlpacaClientFactory>();
        _mockVwapMonitorService = new Mock<IVWAPMonitorService>();

        // Setup Redis mock
        _mockRedisService.Setup(x => x.GetDatabaseAsync(-1)).ReturnsAsync(_mockRedis.Object);

        // Setup configuration
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                // IndexRegime configuration
                ["IndexRegime:SpxMomentumPeriodMinutes"] = "1440",
                ["IndexRegime:VixVolatileThreshold"] = "25.0",
                ["IndexRegime:VixPanicThreshold"] = "35.0",
                ["IndexRegime:VixEuphoricThreshold"] = "12.0",
                ["IndexRegime:SpxMomentumBullThreshold"] = "0.5",
                ["IndexRegime:SpxMomentumBearThreshold"] = "-0.5",
                ["IndexRegime:DivergenceThreshold"] = "2.0",
                ["IndexRegime:RegimeConfirmationMinutes"] = "15",
                ["IndexRegime:CacheExpiryMinutes"] = "5",
                ["IndexRegime:EnableRealTimeUpdates"] = "true",

                // VixResolver configuration
                ["VixResolver:FreshnessThreshold"] = "00:15:00",
                ["VixResolver:CacheStaleThreshold"] = "01:00:00",
                ["VixResolver:MaxRetryAttempts"] = "3",
                ["VixResolver:RetryDelay"] = "00:00:05",
                ["VixResolver:EnableSyntheticCalculation"] = "true",
                ["VixResolver:EnableWebScraping"] = "true",
                ["VixResolver:AllowStaleCache"] = "true",
                ["VixResolver:HaltTradingOnFailure"] = "true",

                // BreadthMonitor configuration
                ["BreadthMonitor:UpdateIntervalSeconds"] = "30",
                ["BreadthMonitor:MinUniverseSize"] = "100",
                ["BreadthMonitor:ExtremeBreadthThreshold"] = "0.9",
                ["BreadthMonitor:DivergenceThreshold"] = "0.3",
                ["BreadthMonitor:MomentumLookbackDays"] = "20",
                ["BreadthMonitor:EnableRealTimeUpdates"] = "true",
                ["BreadthMonitor:CacheExpiryMinutes"] = "5",

                // RealTimeExecution configuration
                ["RealTimeExecution:MonitoringIntervalMs"] = "1000",
                ["RealTimeExecution:SpreadSpikeThreshold"] = "2.0",
                ["RealTimeExecution:LiquidityThreshold"] = "5000",
                ["RealTimeExecution:MaxConcurrentExecutions"] = "10",
                ["RealTimeExecution:DefaultThrottleDuration"] = "00:05:00",
                ["RealTimeExecution:EnableAdaptiveExecution"] = "true",
                ["RealTimeExecution:EnableSpreadMonitoring"] = "true",
                ["RealTimeExecution:EnableVwapGuidance"] = "true"
            })
            .Build();

        // Setup service collection
        var services = new ServiceCollection();
        
        // Add configuration
        services.AddSingleton<IConfiguration>(configuration);
        
        // Add logging
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
        
        // Add mocked dependencies
        services.AddSingleton(_mockMarketDataService.Object);
        services.AddSingleton(_mockRedisService.Object);
        services.AddSingleton(_mockTickStreamService.Object);
        services.AddSingleton(_mockUniverseProvider.Object);
        services.AddSingleton(_mockVixFallbackService.Object);
        services.AddSingleton(_mockPolygonFactory.Object);
        services.AddSingleton(_mockAlpacaFactory.Object);
        services.AddSingleton(_mockVwapMonitorService.Object);
        
        // Add Phase 6 services
        services.AddScoped<IIndexRegimeService, IndexRegimeService>();
        services.AddScoped<IVIXResolverService, VIXResolverService>();
        services.AddScoped<IBreadthMonitorService, BreadthMonitorService>();
        services.AddScoped<IRealTimeExecutionService, RealTimeExecutionService>();

        _serviceProvider = services.BuildServiceProvider();
    }

    [Fact]
    public async Task Phase6Services_ShouldStartAndStopSuccessfully()
    {
        // Arrange
        var indexRegimeService = _serviceProvider.GetRequiredService<IIndexRegimeService>();
        var vixResolverService = _serviceProvider.GetRequiredService<IVIXResolverService>();
        var breadthMonitorService = _serviceProvider.GetRequiredService<IBreadthMonitorService>();
        var realTimeExecutionService = _serviceProvider.GetRequiredService<IRealTimeExecutionService>();

        SetupMockData();

        // Act & Assert - Start all services
        await indexRegimeService.StartMonitoringAsync();
        indexRegimeService.GetStatus().Should().Be(IndexRegimeMonitorStatus.Active);

        await breadthMonitorService.StartMonitoringAsync();
        breadthMonitorService.GetStatus().Should().Be(BreadthMonitorStatus.Active);

        var symbols = new[] { "AAPL", "MSFT", "GOOGL" };
        await realTimeExecutionService.StartMonitoringAsync(symbols);
        realTimeExecutionService.GetStatus().Should().Be(RealTimeExecutionStatus.Active);

        // Stop all services
        await indexRegimeService.StopMonitoringAsync();
        indexRegimeService.GetStatus().Should().Be(IndexRegimeMonitorStatus.Stopped);

        await breadthMonitorService.StopMonitoringAsync();
        breadthMonitorService.GetStatus().Should().Be(BreadthMonitorStatus.Stopped);

        await realTimeExecutionService.StopMonitoringAsync();
        realTimeExecutionService.GetStatus().Should().Be(RealTimeExecutionStatus.Stopped);
    }

    [Fact]
    public async Task Phase6TradingCycle_WithFavorableConditions_ShouldAllowTrading()
    {
        // Arrange
        var indexRegimeService = _serviceProvider.GetRequiredService<IIndexRegimeService>();
        var vixResolverService = _serviceProvider.GetRequiredService<IVIXResolverService>();
        var breadthMonitorService = _serviceProvider.GetRequiredService<IBreadthMonitorService>();
        var realTimeExecutionService = _serviceProvider.GetRequiredService<IRealTimeExecutionService>();

        SetupFavorableMarketConditions();

        // Act - Simulate trading cycle checks
        var indexRegime = await indexRegimeService.RefreshRegimeAsync();
        var vixResult = await vixResolverService.GetCurrentVixAsync();
        var breadthAnalysis = await breadthMonitorService.RefreshBreadthAnalysisAsync();
        var supportsBullish = await breadthMonitorService.SupportsBullishSignalsAsync();
        var executionAllowed = await realTimeExecutionService.IsExecutionAllowedAsync("AAPL");

        // Assert - All conditions should be favorable
        indexRegime.Should().BeOneOf(IndexMarketRegime.TrendingUp, IndexMarketRegime.Sideways);
        vixResult.VixValue.Should().BeLessThan(30m);
        vixResult.ShouldHaltTrading.Should().BeFalse();
        breadthAnalysis.Regime.Should().BeOneOf(BreadthRegime.Healthy, BreadthRegime.Recovering);
        supportsBullish.Should().BeTrue();
        executionAllowed.Should().BeTrue();
    }

    [Fact]
    public async Task Phase6TradingCycle_WithUnfavorableConditions_ShouldBlockTrading()
    {
        // Arrange
        var indexRegimeService = _serviceProvider.GetRequiredService<IIndexRegimeService>();
        var vixResolverService = _serviceProvider.GetRequiredService<IVIXResolverService>();
        var breadthMonitorService = _serviceProvider.GetRequiredService<IBreadthMonitorService>();
        var realTimeExecutionService = _serviceProvider.GetRequiredService<IRealTimeExecutionService>();

        SetupUnfavorableMarketConditions();

        // Act - Simulate trading cycle checks
        var indexRegime = await indexRegimeService.RefreshRegimeAsync();
        var vixResult = await vixResolverService.GetCurrentVixAsync();
        var breadthAnalysis = await breadthMonitorService.RefreshBreadthAnalysisAsync();
        var supportsBullish = await breadthMonitorService.SupportsBullishSignalsAsync();

        // Assert - Conditions should block trading
        var shouldBlockTrading = indexRegime == IndexMarketRegime.Panic ||
                                indexRegime == IndexMarketRegime.Volatile ||
                                vixResult.VixValue > 30m ||
                                vixResult.ShouldHaltTrading ||
                                !supportsBullish;

        shouldBlockTrading.Should().BeTrue();
    }

    [Fact]
    public async Task VIXResolverService_FallbackMechanism_ShouldWorkCorrectly()
    {
        // Arrange
        var vixResolverService = _serviceProvider.GetRequiredService<IVIXResolverService>();
        
        // Setup fallback scenario - primary sources fail, fallback succeeds
        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync(It.Is<string>(s => s == "VIX"), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(new[] { new IndexBar(DateTime.UtcNow, 20.0m, 21.0m, 19.5m, 20.5m, 1000000) }); // VIX data
        
        _mockVixFallbackService.Setup(x => x.CalculateSyntheticVixAsync())
            .ReturnsAsync(18.5m); // Synthetic VIX succeeds

        // Act
        var result = await vixResolverService.GetCurrentVixAsync();

        // Assert
        result.Should().NotBeNull();
        result.VixValue.Should().Be(18.5m);
        result.FallbackLevel.Should().Be(VixFallbackLevel.SyntheticAlpaca);
        result.Quality.Should().Be(VixDataQuality.Synthetic);
        result.IsFresh.Should().BeTrue();
    }

    [Fact]
    public async Task BreadthMonitorService_PositionSizeAdjustment_ShouldWorkCorrectly()
    {
        // Arrange
        var breadthMonitorService = _serviceProvider.GetRequiredService<IBreadthMonitorService>();
        SetupMockBreadthData(BreadthSignalStrength.VeryStrong);

        var baseSize = 100m;

        // Act
        var adjustedSize = await breadthMonitorService.GetBreadthAdjustedPositionSizeAsync(baseSize);

        // Assert
        adjustedSize.Should().BeGreaterThan(baseSize); // Should increase for very strong breadth
        adjustedSize.Should().Be(120m); // 20% increase for very strong
    }

    [Fact]
    public async Task RealTimeExecutionService_ExecutionStrategy_ShouldAdaptToConditions()
    {
        // Arrange
        var realTimeExecutionService = _serviceProvider.GetRequiredService<IRealTimeExecutionService>();
        var symbol = "AAPL";
        
        SetupMockMarketDataForExecution(symbol, goodConditions: true);

        // Act
        var strategy = await realTimeExecutionService.EvaluateExecutionStrategyAsync(symbol, OrderSide.Buy.ToSmaOrderSide(), 100m);

        // Assert
        strategy.Should().NotBeNull();
        strategy.Symbol.Should().Be(symbol);
        strategy.RecommendedOrderType.Should().BeOneOf(SmaOrderType.Market, SmaOrderType.Limit);
        strategy.ConfidenceScore.Should().BeGreaterThan(0);
        strategy.Reasoning.Should().NotBeEmpty();
    }

    [Fact]
    public async Task Phase6Services_DataFreshnessValidation_ShouldEnforceThresholds()
    {
        // Arrange
        var vixResolverService = _serviceProvider.GetRequiredService<IVIXResolverService>();
        
        // Setup stale data (older than 15 minutes)
        var staleBars = TestMockHelpers.CreateVixBars(20m);
        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync(It.Is<string>(s => s == "VIX"), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(staleBars);

        // Act
        var result = await vixResolverService.GetCurrentVixAsync();

        // Assert - Should either get fresh data from fallback or indicate staleness
        if (result.VixValue.HasValue)
        {
            result.FallbackLevel.Should().NotBe(VixFallbackLevel.PolygonWebSocket); // Should have fallen back
        }
        else
        {
            result.ShouldHaltTrading.Should().BeTrue(); // Or should halt trading
        }
    }

    [Fact]
    public async Task Phase6Services_EventHandling_ShouldFireCorrectly()
    {
        // Arrange
        var indexRegimeService = _serviceProvider.GetRequiredService<IIndexRegimeService>();
        var breadthMonitorService = _serviceProvider.GetRequiredService<IBreadthMonitorService>();
        var realTimeExecutionService = _serviceProvider.GetRequiredService<IRealTimeExecutionService>();

        var breadthUpdateEventFired = false;
        var executionDecisionEventFired = false;

        indexRegimeService.RegimeChanged += (_, _) => { /* Event handled */ };
        breadthMonitorService.BreadthAnalysisUpdated += (_, _) => breadthUpdateEventFired = true;
        realTimeExecutionService.ExecutionDecisionMade += (_, _) => executionDecisionEventFired = true;

        SetupMockData();

        // Act
        await indexRegimeService.RefreshRegimeAsync();
        await breadthMonitorService.RefreshBreadthAnalysisAsync();
        await realTimeExecutionService.EvaluateExecutionStrategyAsync("AAPL", OrderSide.Buy.ToSmaOrderSide(), 100m);

        // Assert
        // Note: Events may not fire if regime/conditions don't change
        // This test validates the event infrastructure is in place
        breadthUpdateEventFired.Should().BeTrue();
        executionDecisionEventFired.Should().BeTrue();
    }

    private void SetupMockData()
    {
        SetupFavorableMarketConditions();
    }

    private void SetupFavorableMarketConditions()
    {
        // Setup favorable SPX data (trending up)
        var spxBars = TestMockHelpers.CreateSpxBars(momentum: 0.8m);
        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync(It.Is<string>(s => s == "SPX"), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(spxBars);

        // Setup low VIX
        var vixBars = TestMockHelpers.CreateVixBars(15m);
        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync(It.Is<string>(s => s == "VIX"), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(vixBars);

        // Setup favorable NDX data
        var ndxBars = TestMockHelpers.CreateNdxBars(momentum: 1.0m);
        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync(It.Is<string>(s => s == "NDX"), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(ndxBars);

        // Setup universe
        var symbols = new List<string> { "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA" };
        _mockUniverseProvider.Setup(x => x.GetCachedUniverseAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(symbols);

        // Setup individual stock data (mostly advancing)
        foreach (var symbol in symbols)
        {
            var bars = CreateMockStockBars(symbol, advancing: true);
            var mockPage = TestDataFactory.CreateMockBarPage(bars);
            _mockMarketDataService.Setup(x => x.GetStockBarsAsync(It.Is<string>(s => s == symbol), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
                .ReturnsAsync(mockPage);
        }

        // Setup VWAP data
        SetupMockVwapData();
    }

    private void SetupUnfavorableMarketConditions()
    {
        // Setup panic SPX data (trending down)
        var spxBars = TestMockHelpers.CreateSpxBars(momentum: -2.0m);
        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync(It.Is<string>(s => s == "SPX"), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(spxBars);

        // Setup high VIX (panic level)
        var vixBars = TestMockHelpers.CreateVixBars(40m);
        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync(It.Is<string>(s => s == "VIX"), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(vixBars);

        // Setup declining NDX data
        var ndxBars = TestMockHelpers.CreateNdxBars(momentum: -1.5m);
        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync(It.Is<string>(s => s == "NDX"), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(ndxBars);

        // Setup universe
        var symbols = new List<string> { "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA" };
        _mockUniverseProvider.Setup(x => x.GetCachedUniverseAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(symbols);

        // Setup individual stock data (mostly declining)
        foreach (var symbol in symbols)
        {
            var bars = CreateMockStockBars(symbol, advancing: false);
            var mockPage = TestDataFactory.CreateMockBarPage(bars);
            _mockMarketDataService.Setup(x => x.GetStockBarsAsync(It.Is<string>(s => s == symbol), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
                .ReturnsAsync(mockPage);
        }
    }

    private void SetupMockBreadthData(BreadthSignalStrength strength)
    {
        var cachedAnalysis = new RealTimeBreadthAnalysis
        {
            AnalyzedAt = DateTime.UtcNow.AddMinutes(-1),
            UniverseSize = 5,
            Regime = BreadthRegime.Healthy,
            SignalStrength = strength,
            OverallBreadthScore = 0.8m,
            AdvanceDecline = new AdvanceDeclineStats(),
            MovingAverageBreadth = new MovingAverageBreadthStats(),
            NewHighsLows = new NewHighsLowsStats(),
            Momentum = new BreadthMomentumAnalysis(),
            Divergence = new BreadthDivergenceAnalysis()
        };

        _mockRedis.Setup(x => x.StringGetAsync(It.Is<RedisKey>(k => k == "breadth:analysis:current"), It.IsAny<CommandFlags>()))
            .ReturnsAsync(System.Text.Json.JsonSerializer.Serialize(cachedAnalysis));
    }

    private void SetupMockMarketDataForExecution(string symbol, bool goodConditions)
    {
        var bars = CreateMockStockBars(symbol, advancing: goodConditions);
        var mockPage = TestDataFactory.CreateMockBarPage(bars);
        _mockMarketDataService.Setup(x => x.GetStockBarsAsync(It.Is<string>(s => s == symbol), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(mockPage);
    }

    private void SetupMockVwapData()
    {
        var symbols = new[] { "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA" };
        foreach (var symbol in symbols)
        {
            var vwapData = new VWAPData(
                Symbol: symbol,
                VWAP: 150m,
                CurrentPrice: 150m,
                DeviationPercent: 0m,
                CumulativeVolume: 1000000,
                CumulativeValue: 150000000m,
                Timestamp: DateTime.UtcNow,
                TradeCount: 100,
                Trend: VWAPTrend.AtVWAP
            );

            _mockVwapMonitorService.Setup(x => x.GetCurrentVWAPAsync(symbol))
                .ReturnsAsync(vwapData);
        }
    }

    private List<IIndexBar> CreateMockSpxBars(decimal momentum = 0.5m)
    {
        var basePrice = 4500m;
        var bars = new List<IIndexBar>();
        
        for (int i = 0; i < 10; i++)
        {
            var price = basePrice + (i * momentum * 10);
            var mockBar = new Mock<IIndexBar>();
            mockBar.Setup(x => x.Close).Returns(price);
            mockBar.Setup(x => x.Open).Returns(price - 5);
            mockBar.Setup(x => x.High).Returns(price + 10);
            mockBar.Setup(x => x.Low).Returns(price - 10);
            mockBar.Setup(x => x.Timestamp).Returns(DateTime.UtcNow.AddDays(-i));
            bars.Add(mockBar.Object);
        }

        return bars.OrderBy(b => b.Timestamp).ToList();
    }

    private List<IIndexBar> CreateMockVixBars(decimal vixLevel = 20m, DateTime? timestamp = null)
    {
        var bars = new List<IIndexBar>();
        var mockBar = new Mock<IIndexBar>();
        
        mockBar.Setup(x => x.Close).Returns(vixLevel);
        mockBar.Setup(x => x.Open).Returns(vixLevel - 0.5m);
        mockBar.Setup(x => x.High).Returns(vixLevel + 1.0m);
        mockBar.Setup(x => x.Low).Returns(vixLevel - 1.0m);
        mockBar.Setup(x => x.Timestamp).Returns(timestamp ?? DateTime.UtcNow);
        
        bars.Add(mockBar.Object);
        return bars;
    }

    private List<IIndexBar> CreateMockNdxBars(decimal momentum = 0.5m)
    {
        var basePrice = 15000m;
        var bars = new List<IIndexBar>();
        
        for (int i = 0; i < 10; i++)
        {
            var price = basePrice + (i * momentum * 50);
            var mockBar = new Mock<IIndexBar>();
            mockBar.Setup(x => x.Close).Returns(price);
            mockBar.Setup(x => x.Open).Returns(price - 25);
            mockBar.Setup(x => x.High).Returns(price + 50);
            mockBar.Setup(x => x.Low).Returns(price - 50);
            mockBar.Setup(x => x.Timestamp).Returns(DateTime.UtcNow.AddDays(-i));
            bars.Add(mockBar.Object);
        }

        return bars.OrderBy(b => b.Timestamp).ToList();
    }

    private List<IBar> CreateMockStockBars(string symbol, bool advancing)
    {
        var bars = new List<IBar>();
        var basePrice = symbol switch
        {
            "AAPL" => 150m,
            "MSFT" => 300m,
            "GOOGL" => 2500m,
            "AMZN" => 3000m,
            "TSLA" => 800m,
            _ => 100m
        };

        for (int i = 0; i < 300; i++)
        {
            var change = advancing ? 0.1m : -0.1m;
            var price = basePrice + (i * change);
            var mockBar = new Mock<IBar>();
            
            mockBar.Setup(x => x.Close).Returns(price);
            mockBar.Setup(x => x.Open).Returns(price - (advancing ? -0.5m : 0.5m));
            mockBar.Setup(x => x.High).Returns(price + 2);
            mockBar.Setup(x => x.Low).Returns(price - 2);
            mockBar.Setup(x => x.Volume).Returns(1000000);
            mockBar.Setup(x => x.TimeUtc).Returns(DateTime.UtcNow.AddDays(-i));

            bars.Add(mockBar.Object);
        }

        return bars.OrderBy(b => b.TimeUtc).ToList();
    }

    public void Dispose()
    {
        _serviceProvider?.Dispose();
    }
}
