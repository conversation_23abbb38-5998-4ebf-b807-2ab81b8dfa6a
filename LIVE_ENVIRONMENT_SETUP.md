# Live Environment Setup Guide

## Overview
This guide provides step-by-step instructions for configuring the SmaTrendFollower system for live trading.

## 🔑 API Credentials Setup

### 1. Alpaca Live Trading Keys
```bash
# Required environment variables for live trading
APCA_API_KEY_ID=your_live_alpaca_key_here
APCA_API_SECRET_KEY=your_live_alpaca_secret_here
APCA_API_ENV=live
```

### 2. Polygon API Configuration
```bash
# Market data API (already configured)
POLY_API_KEY=********************************
```

### 3. Discord Notifications
```bash
# Notification system (already configured)
DISCORD_BOT_TOKEN=MTM4NTA1OTI3MDMzNjMxNTQ1NA.GtTKUd.fuCC2ZI-H-tTLZl41YF3gZj-w3gPbv_Xep8NoE
DISCORD_CHANNEL_ID=1385057459814797383
```

## 🛡️ Live Safety Configuration

### Conservative Live Settings (.env.live)
```bash
# Alpaca API Configuration for Live Trading
APCA_API_KEY_ID=your_live_alpaca_key_here
APCA_API_SECRET_KEY=your_live_alpaca_secret_here
APCA_API_ENV=live

# Polygon API Configuration
POLY_API_KEY=********************************

# Discord Notification Configuration
DISCORD_BOT_TOKEN=MTM4NTA1OTI3MDMzNjMxNTQ1NA.GtTKUd.fuCC2ZI-H-tTLZl41YF3gZj-w3gPbv_Xep8NoE
DISCORD_CHANNEL_ID=1385057459814797383

# Conservative Safety Configuration for Live Trading
SAFETY_ALLOWED_ENVIRONMENT=Live
SAFETY_MAX_DAILY_LOSS=120
SAFETY_MAX_POSITIONS=5
SAFETY_MAX_SINGLE_TRADE_VALUE=1200
SAFETY_MIN_ACCOUNT_EQUITY=5000
SAFETY_REQUIRE_CONFIRMATION=true
SAFETY_DRY_RUN_MODE=false

# Redis Configuration (optional but recommended)
REDIS_URL=192.168.1.168:6379
REDIS_DATABASE=0
```

## 🚀 Deployment Steps

### 1. Pre-Deployment Validation
```bash
# Backup current configuration
cp .env .env.backup

# Validate current system
dotnet run -- validate

# Run comprehensive tests
dotnet test --verbosity normal
```

### 2. Live Environment Configuration
```bash
# Copy live configuration
cp .env.live .env

# Validate live configuration
dotnet run -- validate

# Test live API connectivity (dry run)
dotnet run -- --dry-run account-status
```

### 3. Gradual Deployment
```bash
# Phase 1: Dry run with live APIs
dotnet run -- --dry-run run

# Phase 2: Live trading with minimal positions
dotnet run -- --confirm run

# Phase 3: Full live trading (after validation)
dotnet run -- --confirm run
```

## 📊 Monitoring Setup

### 1. Real-Time Monitoring Commands
```bash
# System health check
dotnet run -- health

# Trading metrics
dotnet run -- metrics

# Live market data
dotnet run -- live

# Account status
dotnet run -- account-status
```

### 2. Automated Monitoring Script
```powershell
# monitor-live-trading.ps1
while ($true) {
    Write-Host "=== $(Get-Date) ===" -ForegroundColor Green
    
    # Check system health
    dotnet run --project SmaTrendFollower.Console -- health
    
    # Check account status
    dotnet run --project SmaTrendFollower.Console -- account-status
    
    # Check metrics
    dotnet run --project SmaTrendFollower.Console -- metrics
    
    # Wait 5 minutes
    Start-Sleep -Seconds 300
}
```

### 3. Discord Alert Integration
The system will automatically send Discord notifications for:
- Trade executions
- Stop loss triggers
- Safety limit breaches
- System errors
- Daily performance summaries

## 🔧 Performance Optimization

### 1. Database Optimization
```bash
# Ensure SQLite database is optimized
dotnet run -- optimize-database

# Clear old cache data (optional)
dotnet run -- clear-cache --older-than 30days
```

### 2. Redis Configuration
```bash
# Start Redis server (if not running)
redis-server

# Verify Redis connection
redis-cli ping
```

### 3. System Resources
- **Minimum RAM**: 4GB
- **Recommended RAM**: 8GB+
- **CPU**: Multi-core recommended for parallel signal processing
- **Network**: Stable internet connection for real-time data

## 🚨 Emergency Procedures

### 1. Emergency Stop
```bash
# Immediate halt of all trading
dotnet run -- emergency-stop

# Cancel all pending orders
dotnet run -- cancel-all-orders

# Enable dry run mode
dotnet run -- --dry-run validate
```

### 2. Rollback to Paper Trading
```bash
# Restore paper trading configuration
cp .env.backup .env

# Verify paper trading mode
dotnet run -- validate
```

### 3. System Recovery
```bash
# Restart with safe defaults
dotnet run -- --dry-run --paper-only validate

# Check system integrity
dotnet test --verbosity normal

# Gradual re-enable
dotnet run -- --confirm validate
```

## 📋 Go-Live Checklist

### Pre-Live Requirements ✅
- [x] All unit tests passing (100%)
- [x] Safety systems validated
- [x] Environment detection working
- [x] Risk management tested
- [x] Market regime detection functional
- [x] Signal generation validated
- [x] Order execution tested
- [x] Emergency procedures documented

### Live Deployment Requirements
- [ ] Live Alpaca API credentials obtained
- [ ] Live account funded with minimum $5,000
- [ ] Live API connectivity tested
- [ ] Live environment configuration validated
- [ ] Monitoring systems active
- [ ] Emergency procedures tested
- [ ] Backup and recovery plan ready

### Post-Deployment Monitoring
- [ ] 24-hour monitoring of first live trades
- [ ] Daily performance review
- [ ] Weekly safety system audit
- [ ] Monthly configuration review

## 🎯 Success Metrics

### Daily Targets
- **Max Daily Loss**: Stay within $120 limit
- **Position Count**: Maximum 5 concurrent positions
- **Win Rate**: Target >50% (long-term)
- **Risk-Adjusted Returns**: Positive Sharpe ratio

### Weekly Reviews
- **Total P&L**: Track cumulative performance
- **Risk Metrics**: Monitor drawdowns and volatility
- **System Health**: Ensure all components functioning
- **Safety Compliance**: Verify all limits respected

---
*Last Updated: 2025-06-21*
*Status: Ready for Live Deployment*
