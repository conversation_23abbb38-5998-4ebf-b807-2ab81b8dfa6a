using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using System.Collections.Concurrent;
using System.Text.Json;
using StackExchange.Redis;

namespace SmaTrendFollower.Services;

/// <summary>
/// Production-ready smart trade throttler service for limiting trade frequency and preventing overtrading
/// Implements per-symbol frequency limits, tick-range based throttling, and crowded setup detection
/// </summary>
public class SmartTradeThrottler : ISmartTradeThrottler
{
    private readonly ILogger<SmartTradeThrottler> _logger;
    private readonly IOptimizedRedisConnectionService _redisService;
    private readonly ITimeProvider _timeProvider;
    private readonly ITickVolatilityGuard _volatilityGuard;

    private TradeThrottlerConfig _config;
    private ThrottlingServiceStatus _status;
    private readonly ConcurrentDictionary<string, SymbolThrottlingRules> _symbolRules;
    private readonly ConcurrentDictionary<string, ThrottlingStatus> _throttlingStates;
    private readonly ConcurrentDictionary<string, List<ThrottlerTradeExecution>> _recentTrades; // Renamed to avoid conflict
    private readonly ConcurrentDictionary<string, byte> _monitoredSymbols; // Using as concurrent set
    private readonly ConcurrentDictionary<string, DateTime> _overrides;
    private readonly SemaphoreSlim _operationLock;
    private readonly Timer _maintenanceTimer;
    private bool _disposed;

    // Redis key patterns
    private const string ThrottleStatusKeyPattern = "throttle:status:{0}";
    private const string TradeHistoryKeyPattern = "throttle:trades:{0}";
    private const string ThrottleEventsKeyPattern = "throttle:events:{0}";
    private const string OverrideKeyPattern = "throttle:override:{0}";
    private const string StatsKeyPattern = "throttle:stats:{0}";

    // === Events ===

    public event EventHandler<TradeThrottledEventArgs>? TradeThrottled;
    public event EventHandler<ThrottlingRulesUpdatedEventArgs>? ThrottlingRulesUpdated;

#pragma warning disable CS0067 // Event is never used - reserved for future implementation
    public event EventHandler<CooldownExpiredEventArgs>? CooldownExpired;
#pragma warning restore CS0067

    public SmartTradeThrottler(
        ILogger<SmartTradeThrottler> logger,
        IOptimizedRedisConnectionService redisService,
        ITimeProvider timeProvider,
        ITickVolatilityGuard volatilityGuard)
    {
        _logger = logger;
        _redisService = redisService;
        _timeProvider = timeProvider;
        _volatilityGuard = volatilityGuard;

        _config = new TradeThrottlerConfig();
        _status = ThrottlingServiceStatus.Stopped;
        _symbolRules = new ConcurrentDictionary<string, SymbolThrottlingRules>();
        _throttlingStates = new ConcurrentDictionary<string, ThrottlingStatus>();
        _recentTrades = new ConcurrentDictionary<string, List<ThrottlerTradeExecution>>();
        _monitoredSymbols = new ConcurrentDictionary<string, byte>(); // Using as concurrent set
        _overrides = new ConcurrentDictionary<string, DateTime>();
        _operationLock = new SemaphoreSlim(1, 1);

        // Maintenance timer for cleanup and cooldown management
        _maintenanceTimer = new Timer(PerformMaintenance, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
    }

    // === Core Methods ===

    public async Task StartThrottlingAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default)
    {
        await _operationLock.WaitAsync(cancellationToken);
        try
        {
            _status = ThrottlingServiceStatus.Starting;
            var symbolList = symbols.ToList();

            _logger.LogInformation("Starting trade throttling for {Count} symbols: {Symbols}",
                symbolList.Count, string.Join(", ", symbolList));

            // Initialize throttling states for symbols
            foreach (var symbol in symbolList)
            {
                _monitoredSymbols.TryAdd(symbol, 0); // Add to concurrent set
                _recentTrades[symbol] = new List<ThrottlerTradeExecution>();

                // Load existing state from Redis
                await LoadThrottlingStateAsync(symbol);
                await LoadTradeHistoryAsync(symbol);
            }

            _status = ThrottlingServiceStatus.Active;
            _logger.LogInformation("Trade throttling started for {Count} symbols", symbolList.Count);
        }
        catch (Exception ex)
        {
            _status = ThrottlingServiceStatus.Error;
            _logger.LogError(ex, "Failed to start trade throttling");
            throw;
        }
        finally
        {
            _operationLock.Release();
        }
    }

    public async Task StopThrottlingAsync(CancellationToken cancellationToken = default)
    {
        await _operationLock.WaitAsync(cancellationToken);
        try
        {
            _logger.LogInformation("Stopping trade throttling");

            // Save all states to Redis before stopping
            foreach (var symbol in _monitoredSymbols.Keys)
            {
                if (_throttlingStates.TryGetValue(symbol, out var state))
                {
                    await SaveThrottlingStateAsync(symbol, state);
                }

                if (_recentTrades.TryGetValue(symbol, out var trades))
                {
                    await SaveTradeHistoryAsync(symbol, trades);
                }
            }

            _monitoredSymbols.Clear();
            _symbolRules.Clear();
            _throttlingStates.Clear();
            _recentTrades.Clear();
            _overrides.Clear();
            _status = ThrottlingServiceStatus.Stopped;

            _logger.LogInformation("Trade throttling stopped");
        }
        catch (Exception ex)
        {
            _status = ThrottlingServiceStatus.Error;
            _logger.LogError(ex, "Failed to stop trade throttling");
            throw;
        }
        finally
        {
            _operationLock.Release();
        }
    }

    public async Task<TradeThrottleDecision> ShouldAllowTradeAsync(string symbol, TradeRequest tradeRequest)
    {
        if (_status != ThrottlingServiceStatus.Active || !_monitoredSymbols.ContainsKey(symbol))
        {
            return new TradeThrottleDecision(
                IsAllowed: true,
                Reason: ThrottleReason.None,
                ReasonDescription: "Throttling not active for symbol",
                SuggestedDelay: null,
                ConfidenceLevel: 1.0m,
                DecisionTime: _timeProvider.UtcNow,
                Metrics: CreateCurrentMetrics(symbol, tradeRequest)
            );
        }

        try
        {
            // Check for active override
            if (_overrides.TryGetValue(symbol, out var overrideExpiry) && _timeProvider.UtcNow < overrideExpiry)
            {
                return new TradeThrottleDecision(
                    IsAllowed: true,
                    Reason: ThrottleReason.SystemOverride,
                    ReasonDescription: "Override active",
                    SuggestedDelay: null,
                    ConfidenceLevel: 1.0m,
                    DecisionTime: _timeProvider.UtcNow,
                    Metrics: CreateCurrentMetrics(symbol, tradeRequest)
                );
            }

            // Get current throttling state
            var currentState = _throttlingStates.GetValueOrDefault(symbol) ?? CreateInitialThrottlingState(symbol);
            var symbolRules = GetEffectiveRules(symbol);
            var recentTrades = _recentTrades.GetValueOrDefault(symbol) ?? new List<ThrottlerTradeExecution>();

            // Perform throttling checks
            var decision = await EvaluateThrottlingRulesAsync(symbol, tradeRequest, currentState, symbolRules, recentTrades);

            // Record throttling event if trade is blocked
            if (!decision.IsAllowed)
            {
                await RecordThrottlingEventAsync(symbol, ThrottleEventType.TradeBlocked, decision.Reason,
                    decision.ReasonDescription, tradeRequest);

                TradeThrottled?.Invoke(this, new TradeThrottledEventArgs
                {
                    Symbol = symbol,
                    Request = tradeRequest,
                    Decision = decision,
                    ThrottleTime = _timeProvider.UtcNow
                });
            }
            else
            {
                await RecordThrottlingEventAsync(symbol, ThrottleEventType.TradeAllowed, ThrottleReason.None,
                    "Trade allowed", tradeRequest);
            }

            return decision;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to evaluate trade throttling for {Symbol}", symbol);

            // Default to allowing trade on error to avoid blocking legitimate trades
            return new TradeThrottleDecision(
                IsAllowed: true,
                Reason: ThrottleReason.None,
                ReasonDescription: $"Error in throttling evaluation: {ex.Message}",
                SuggestedDelay: null,
                ConfidenceLevel: 0.1m,
                DecisionTime: _timeProvider.UtcNow,
                Metrics: CreateCurrentMetrics(symbol, tradeRequest)
            );
        }
    }

    public async Task RecordTradeExecutionAsync(string symbol, ThrottlerTradeExecution execution)
    {
        if (_status != ThrottlingServiceStatus.Active || !_monitoredSymbols.ContainsKey(symbol))
            return;

        try
        {
            // Add to recent trades
            var trades = _recentTrades.GetOrAdd(symbol, _ => new List<ThrottlerTradeExecution>());
            lock (trades)
            {
                trades.Add(execution);

                // Keep only recent trades within the statistics window
                var cutoffTime = _timeProvider.UtcNow - _config.StatisticsWindow;
                trades.RemoveAll(t => t.ExecutionTime < cutoffTime);
            }

            // Update throttling state
            var currentState = _throttlingStates.GetValueOrDefault(symbol) ?? CreateInitialThrottlingState(symbol);
            var updatedState = UpdateThrottlingStateAfterTrade(currentState, execution);
            _throttlingStates[symbol] = updatedState;

            // Save to Redis
            await SaveThrottlingStateAsync(symbol, updatedState);
            await SaveTradeHistoryAsync(symbol, trades);

            // Update statistics
            await UpdateTradeFrequencyStatsAsync(symbol, execution);

            _logger.LogDebug("Recorded trade execution for {Symbol}: {Direction} {Quantity} @ {Price}",
                symbol, execution.Direction, execution.ExecutedQuantity, execution.ExecutedPrice);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to record trade execution for {Symbol}", symbol);
        }
    }

    public async Task<ThrottlingStatus?> GetThrottlingStatusAsync(string symbol)
    {
        if (_throttlingStates.TryGetValue(symbol, out var status))
            return status;

        // Try loading from Redis
        return await LoadThrottlingStateAsync(symbol);
    }

    public async Task<TradeFrequencyStats?> GetTradeFrequencyStatsAsync(string symbol)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(StatsKeyPattern, symbol);
            var json = await database.StringGetAsync(key);

            if (!json.HasValue)
                return null;

            return JsonSerializer.Deserialize<TradeFrequencyStats>(json!);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get trade frequency stats for {Symbol}", symbol);
            return null;
        }
    }

    public async Task OverrideThrottlingAsync(string symbol, TimeSpan duration, string reason)
    {
        try
        {
            var expiryTime = _timeProvider.UtcNow.Add(duration);
            _overrides[symbol] = expiryTime;

            // Save to Redis
            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(OverrideKeyPattern, symbol);
            await database.StringSetAsync(key, expiryTime.ToString("O"), duration);

            await RecordThrottlingEventAsync(symbol, ThrottleEventType.OverrideApplied, ThrottleReason.SystemOverride,
                $"Override applied: {reason}", null, duration);

            _logger.LogInformation("Applied throttling override for {Symbol} for {Duration}: {Reason}",
                symbol, duration, reason);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to apply throttling override for {Symbol}", symbol);
        }
    }

    public async Task ResetThrottlingAsync(string symbol)
    {
        try
        {
            // Reset in-memory state
            _throttlingStates.TryRemove(symbol, out _);
            _recentTrades.TryRemove(symbol, out _);
            _overrides.TryRemove(symbol, out _);

            // Clear Redis data
            var database = await _redisService.GetDatabaseAsync();
            var keys = new[]
            {
                string.Format(ThrottleStatusKeyPattern, symbol),
                string.Format(TradeHistoryKeyPattern, symbol),
                string.Format(OverrideKeyPattern, symbol),
                string.Format(StatsKeyPattern, symbol)
            };

            foreach (var key in keys)
            {
                await database.KeyDeleteAsync(key);
            }

            _logger.LogInformation("Reset throttling state for {Symbol}", symbol);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to reset throttling for {Symbol}", symbol);
        }
    }

    public async Task<IEnumerable<ThrottlingEvent>> GetRecentThrottlingEventsAsync(string symbol, int hours = 24)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(ThrottleEventsKeyPattern, symbol);
            var events = await database.ListRangeAsync(key, 0, -1);

            var cutoffTime = _timeProvider.UtcNow.AddHours(-hours);

            return events
                .Select(e => JsonSerializer.Deserialize<ThrottlingEvent>(e!))
                .Where(e => e.EventTime >= cutoffTime)
                .OrderByDescending(e => e.EventTime);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get recent throttling events for {Symbol}", symbol);
            return Enumerable.Empty<ThrottlingEvent>();
        }
    }

    // === Configuration ===

    public Task UpdateConfigurationAsync(TradeThrottlerConfig config)
    {
        _config = config;
        _logger.LogInformation("Updated trade throttler configuration");
        return Task.CompletedTask;
    }

    public async Task UpdateSymbolRulesAsync(string symbol, SymbolThrottlingRules rules)
    {
        var oldRules = _symbolRules.GetValueOrDefault(symbol);
        _symbolRules[symbol] = rules;

        if (oldRules != null)
        {
            ThrottlingRulesUpdated?.Invoke(this, new ThrottlingRulesUpdatedEventArgs
            {
                Symbol = symbol,
                OldRules = oldRules,
                NewRules = rules,
                UpdateTime = _timeProvider.UtcNow
            });
        }

        _logger.LogInformation("Updated throttling rules for {Symbol}", symbol);
        await Task.CompletedTask;
    }

    public ThrottlingServiceStatus GetStatus() => _status;

    public IEnumerable<string> GetThrottledSymbols()
    {
        return _throttlingStates
            .Where(kvp => kvp.Value.IsThrottled)
            .Select(kvp => kvp.Key)
            .ToList();
    }

    public IEnumerable<string> GetMonitoredSymbols() => _monitoredSymbols.Keys;

    // === Private Helper Methods ===

    private async Task<TradeThrottleDecision> EvaluateThrottlingRulesAsync(
        string symbol,
        TradeRequest request,
        ThrottlingStatus currentState,
        SymbolThrottlingRules rules,
        List<ThrottlerTradeExecution> recentTrades)
    {
        var now = _timeProvider.UtcNow;
        var metrics = CreateCurrentMetrics(symbol, request);

        // Check frequency limits
        var hourlyLimit = rules.MaxTradesPerHour ?? _config.MaxTradesPerHour;
        var dailyLimit = rules.MaxTradesPerDay ?? _config.MaxTradesPerDay;

        var tradesLastHour = recentTrades.Count(t => (now - t.ExecutionTime).TotalHours <= 1);
        var tradesLastDay = recentTrades.Count(t => (now - t.ExecutionTime).TotalDays <= 1);

        if (tradesLastHour >= hourlyLimit)
        {
            return CreateThrottleDecision(false, ThrottleReason.FrequencyLimit,
                $"Hourly limit exceeded: {tradesLastHour}/{hourlyLimit}",
                TimeSpan.FromHours(1) - (now - recentTrades.Where(t => (now - t.ExecutionTime).TotalHours <= 1).Min(t => t.ExecutionTime)),
                metrics);
        }

        if (tradesLastDay >= dailyLimit)
        {
            return CreateThrottleDecision(false, ThrottleReason.FrequencyLimit,
                $"Daily limit exceeded: {tradesLastDay}/{dailyLimit}",
                TimeSpan.FromDays(1) - (now - recentTrades.Where(t => (now - t.ExecutionTime).TotalDays <= 1).Min(t => t.ExecutionTime)),
                metrics);
        }

        // Check minimum time between trades
        var minTimeBetween = rules.MinTimeBetweenTrades ?? _config.MinTimeBetweenTrades;
        if (currentState.LastTradeTime.HasValue)
        {
            var timeSinceLastTrade = now - currentState.LastTradeTime.Value;
            if (timeSinceLastTrade < minTimeBetween)
            {
                return CreateThrottleDecision(false, ThrottleReason.TimeBetweenTrades,
                    $"Minimum time not elapsed: {timeSinceLastTrade.TotalMinutes:F1}/{minTimeBetween.TotalMinutes:F1} minutes",
                    minTimeBetween - timeSinceLastTrade,
                    metrics);
            }
        }

        // Check position size limits
        var maxPositionPercent = rules.MaxPositionSizePercent ?? _config.MaxPositionSizePercent;
        if (metrics.PositionSizeRatio > maxPositionPercent)
        {
            return CreateThrottleDecision(false, ThrottleReason.PositionSizeLimit,
                $"Position size too large: {metrics.PositionSizeRatio:P2} > {maxPositionPercent:P2}",
                null, metrics);
        }

        // Check volatility threshold
        if (_config.EnableVolatilityThrottling && request.CurrentVolatility > _config.VolatilityThreshold)
        {
            return CreateThrottleDecision(false, ThrottleReason.VolatilityThreshold,
                $"Volatility too high: {request.CurrentVolatility:P2} > {_config.VolatilityThreshold:P2}",
                _config.CooldownPeriod, metrics);
        }

        // Check tick range throttling
        if (_config.EnableTickRangeThrottling)
        {
            var tickRangeThreshold = rules.TickRangeThreshold ?? 0.02m; // 2% default
            if (request.TickRange > tickRangeThreshold)
            {
                return CreateThrottleDecision(false, ThrottleReason.TickRangeLimit,
                    $"Tick range too wide: {request.TickRange:P2} > {tickRangeThreshold:P2}",
                    TimeSpan.FromMinutes(15), metrics);
            }
        }

        // Check for crowded setups
        if (_config.EnableCrowdedSetupDetection && metrics.CrowdingScore > 0.8m)
        {
            return CreateThrottleDecision(false, ThrottleReason.CrowdedSetup,
                $"Crowded setup detected: score {metrics.CrowdingScore:F2}",
                TimeSpan.FromMinutes(30), metrics);
        }

        // Check signal strength for weak signals
        if (request.SignalStrength == TradeSignalStrength.Weak && tradesLastHour > hourlyLimit / 2)
        {
            return CreateThrottleDecision(false, ThrottleReason.InsufficientSignalStrength,
                "Weak signal with high recent activity",
                TimeSpan.FromMinutes(10), metrics);
        }

        // All checks passed
        await Task.CompletedTask;
        return CreateThrottleDecision(true, ThrottleReason.None, "Trade allowed", null, metrics);
    }

    private TradeThrottleDecision CreateThrottleDecision(bool isAllowed, ThrottleReason reason, string description,
        TimeSpan? suggestedDelay, ThrottlingMetrics metrics)
    {
        return new TradeThrottleDecision(
            IsAllowed: isAllowed,
            Reason: reason,
            ReasonDescription: description,
            SuggestedDelay: suggestedDelay,
            ConfidenceLevel: isAllowed ? 0.9m : 0.95m,
            DecisionTime: _timeProvider.UtcNow,
            Metrics: metrics
        );
    }

    private ThrottlingMetrics CreateCurrentMetrics(string symbol, TradeRequest request)
    {
        var recentTrades = _recentTrades.GetValueOrDefault(symbol) ?? new List<ThrottlerTradeExecution>();
        var recentTradeCount = recentTrades.Count(t => (_timeProvider.UtcNow - t.ExecutionTime).TotalHours <= 1);

        // Simple crowding score based on recent trade frequency and volatility
        var crowdingScore = Math.Min(1.0m, (recentTradeCount / (decimal)_config.MaxTradesPerHour) +
                                           (request.CurrentVolatility / _config.VolatilityThreshold) * 0.3m);

        return new ThrottlingMetrics(
            CurrentVolatility: request.CurrentVolatility,
            TickRange: request.TickRange,
            RecentTradeCount: recentTradeCount,
            PositionSizeRatio: request.Quantity * request.Price / 100000m, // Simplified calculation
            CrowdingScore: crowdingScore,
            MetricsTime: _timeProvider.UtcNow
        );
    }

    private SymbolThrottlingRules GetEffectiveRules(string symbol)
    {
        return _symbolRules.GetValueOrDefault(symbol) ?? new SymbolThrottlingRules(symbol);
    }

    private ThrottlingStatus CreateInitialThrottlingState(string symbol)
    {
        return new ThrottlingStatus(
            Symbol: symbol,
            IsThrottled: false,
            CurrentReason: ThrottleReason.None,
            ThrottleStartTime: null,
            ThrottleEndTime: null,
            TradesInLastHour: 0,
            TradesInLastDay: 0,
            LastTradeTime: null,
            TimeUntilNextAllowed: null,
            IsInCooldown: false,
            HasOverride: false
        );
    }

    private ThrottlingStatus UpdateThrottlingStateAfterTrade(ThrottlingStatus currentState, ThrottlerTradeExecution execution)
    {
        var now = _timeProvider.UtcNow;
        var recentTrades = _recentTrades.GetValueOrDefault(execution.Symbol) ?? new List<ThrottlerTradeExecution>();

        var tradesLastHour = recentTrades.Count(t => (now - t.ExecutionTime).TotalHours <= 1);
        var tradesLastDay = recentTrades.Count(t => (now - t.ExecutionTime).TotalDays <= 1);

        return currentState with
        {
            LastTradeTime = execution.ExecutionTime,
            TradesInLastHour = tradesLastHour,
            TradesInLastDay = tradesLastDay,
            TimeUntilNextAllowed = _config.MinTimeBetweenTrades
        };
    }

    private async Task<ThrottlingStatus?> LoadThrottlingStateAsync(string symbol)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(ThrottleStatusKeyPattern, symbol);
            var json = await database.StringGetAsync(key);

            if (json.HasValue)
            {
                var state = JsonSerializer.Deserialize<ThrottlingStatus>(json!);
                _throttlingStates[symbol] = state;
                return state;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load throttling state for {Symbol}", symbol);
        }

        return null;
    }

    private async Task SaveThrottlingStateAsync(string symbol, ThrottlingStatus state)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(ThrottleStatusKeyPattern, symbol);
            var json = JsonSerializer.Serialize(state);
            await database.StringSetAsync(key, json, TimeSpan.FromDays(7));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save throttling state for {Symbol}", symbol);
        }
    }

    private async Task LoadTradeHistoryAsync(string symbol)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(TradeHistoryKeyPattern, symbol);
            var trades = await database.ListRangeAsync(key, 0, -1);

            var tradeList = trades
                .Select(t => JsonSerializer.Deserialize<ThrottlerTradeExecution>(t!))
                .Where(t => (_timeProvider.UtcNow - t.ExecutionTime) <= _config.StatisticsWindow)
                .ToList();

            _recentTrades[symbol] = tradeList;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load trade history for {Symbol}", symbol);
        }
    }

    private async Task SaveTradeHistoryAsync(string symbol, List<ThrottlerTradeExecution> trades)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(TradeHistoryKeyPattern, symbol);

            // Clear existing and add current trades
            await database.KeyDeleteAsync(key);

            if (trades.Any())
            {
                var tradeJsons = trades.Select(t => (RedisValue)JsonSerializer.Serialize(t)).ToArray();
                await database.ListRightPushAsync(key, tradeJsons);
                await database.KeyExpireAsync(key, _config.StatisticsWindow);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save trade history for {Symbol}", symbol);
        }
    }

    private async Task RecordThrottlingEventAsync(string symbol, ThrottleEventType eventType, ThrottleReason reason,
        string description, TradeRequest? request = null, TimeSpan? duration = null)
    {
        try
        {
            var throttlingEvent = new ThrottlingEvent(
                Symbol: symbol,
                EventType: eventType,
                Reason: reason,
                Description: description,
                EventTime: _timeProvider.UtcNow,
                RelatedRequest: request,
                Duration: duration
            );

            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(ThrottleEventsKeyPattern, symbol);
            var json = JsonSerializer.Serialize(throttlingEvent);

            await database.ListLeftPushAsync(key, json);
            await database.ListTrimAsync(key, 0, 499); // Keep last 500 events
            await database.KeyExpireAsync(key, TimeSpan.FromDays(7));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to record throttling event for {Symbol}", symbol);
        }
    }

    private async Task UpdateTradeFrequencyStatsAsync(string symbol, ThrottlerTradeExecution execution)
    {
        try
        {
            var recentTrades = _recentTrades.GetValueOrDefault(symbol) ?? new List<ThrottlerTradeExecution>();
            var now = _timeProvider.UtcNow;

            var tradesLastHour = recentTrades.Count(t => (now - t.ExecutionTime).TotalHours <= 1);
            var tradesLastDay = recentTrades.Count(t => (now - t.ExecutionTime).TotalDays <= 1);

            var avgTimeBetween = recentTrades.Count > 1 ?
                recentTrades.Zip(recentTrades.Skip(1), (a, b) => (a.ExecutionTime - b.ExecutionTime).TotalMinutes).Average() : 0;

            var avgPositionSize = recentTrades.Any() ? recentTrades.Average(t => t.ExecutedQuantity) : 0;

            var stats = new TradeFrequencyStats(
                Symbol: symbol,
                TotalTrades: recentTrades.Count,
                TradesLastHour: tradesLastHour,
                TradesLastDay: tradesLastDay,
                AverageTimeBetweenTrades: (decimal)avgTimeBetween,
                TradeFrequencyTrend: CalculateFrequencyTrend(recentTrades),
                LastTradeTime: execution.ExecutionTime,
                AveragePositionSize: avgPositionSize,
                WinRate: 0.5m, // Would need P&L data to calculate
                StatisticsTime: now
            );

            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(StatsKeyPattern, symbol);
            var json = JsonSerializer.Serialize(stats);
            await database.StringSetAsync(key, json, TimeSpan.FromHours(24));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update trade frequency stats for {Symbol}", symbol);
        }
    }

    private decimal CalculateFrequencyTrend(List<ThrottlerTradeExecution> trades)
    {
        if (trades.Count < 4) return 0;

        var now = _timeProvider.UtcNow;
        var recentHalf = trades.Where(t => (now - t.ExecutionTime).TotalHours <= 12).Count();
        var olderHalf = trades.Where(t => (now - t.ExecutionTime).TotalHours > 12 && (now - t.ExecutionTime).TotalHours <= 24).Count();

        if (olderHalf == 0) return 0;
        return (decimal)recentHalf / olderHalf - 1; // Positive = increasing frequency
    }

    private void PerformMaintenance(object? state)
    {
        _ = Task.Run(async () =>
        {
            try
            {
                var now = _timeProvider.UtcNow;

                // Check for expired overrides
                var expiredOverrides = _overrides.Where(kvp => now >= kvp.Value).ToList();
                foreach (var kvp in expiredOverrides)
                {
                    _overrides.TryRemove(kvp.Key, out _);

                    await RecordThrottlingEventAsync(kvp.Key, ThrottleEventType.OverrideExpired,
                        ThrottleReason.SystemOverride, "Override expired");

                    _logger.LogInformation("Throttling override expired for {Symbol}", kvp.Key);
                }

                // Clean up old trade data
                foreach (var symbol in _monitoredSymbols.Keys)
                {
                    if (_recentTrades.TryGetValue(symbol, out var trades))
                    {
                        lock (trades)
                        {
                            var cutoffTime = now - _config.StatisticsWindow;
                            var removedCount = trades.RemoveAll(t => t.ExecutionTime < cutoffTime);

                            if (removedCount > 0)
                            {
                                _logger.LogDebug("Cleaned up {Count} old trades for {Symbol}", removedCount, symbol);
                            }
                        }
                    }
                }

                // Check for cooldown expirations
                foreach (var kvp in _throttlingStates.ToList())
                {
                    var state = kvp.Value;
                    if (state.IsInCooldown && state.ThrottleEndTime.HasValue && now >= state.ThrottleEndTime.Value)
                    {
                        var updatedState = state with
                        {
                            IsThrottled = false,
                            IsInCooldown = false,
                            CurrentReason = ThrottleReason.None,
                            ThrottleEndTime = null
                        };

                        _throttlingStates[kvp.Key] = updatedState;
                        await SaveThrottlingStateAsync(kvp.Key, updatedState);

                        CooldownExpired?.Invoke(this, new CooldownExpiredEventArgs
                        {
                            Symbol = kvp.Key,
                            CooldownDuration = now - state.ThrottleStartTime!.Value,
                            ExpirationTime = now,
                            OriginalReason = state.CurrentReason
                        });

                        _logger.LogInformation("Cooldown expired for {Symbol}", kvp.Key);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during throttling maintenance");
            }
        });
    }

    public void Dispose()
    {
        if (_disposed) return;

        _maintenanceTimer?.Dispose();
        _operationLock?.Dispose();
        _disposed = true;
    }
}