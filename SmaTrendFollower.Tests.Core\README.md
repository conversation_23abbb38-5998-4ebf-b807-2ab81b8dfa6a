# SmaTrendFollower.Tests.Core

This is the new core test project for SmaTrendFollower, replacing the legacy test suite.

## Overview

This test project provides a clean, modern testing infrastructure with:

- **xUnit** as the test framework
- **FluentAssertions** for expressive assertions
- **NSubstitute** for mocking
- **RichardSzalay.MockHttp** for HTTP mocking
- **StackExchange.Redis** for Redis testing

## Test Helpers

### InMemoryRedis
Located in `TestHelpers/InMemoryRedis.cs`, provides utilities for Redis testing:

```csharp
// Create a Redis test fixture
using var redisFixture = new RedisTestFixture();
var database = redisFixture.Database;

// Use Redis in tests
await database.StringSetAsync("key", "value");
var result = await database.StringGetAsync("key");

// Automatic cleanup on disposal
```

### MockPolygon
Located in `TestHelpers/MockPolygon.cs`, provides utilities for mocking Polygon.io API:

```csharp
// Create mock Polygon client
using var mockPolygon = new MockPolygon();

// Mock stock bars
var bars = new[] { MockPolygon.CreateSampleBar(timestamp, 100m, 105m, 99m, 104m, 1000000) };
mockPolygon.MockStockBars("AAPL", bars);

// Mock rate limits, errors, etc.
mockPolygon.MockRateLimit("https://api.polygon.io/*");
```

## Running Tests

```bash
# Run all tests
dotnet test SmaTrendFollower.Tests.Core

# Run with coverage
dotnet test SmaTrendFollower.Tests.Core --collect:"XPlat Code Coverage"

# Run specific test
dotnet test SmaTrendFollower.Tests.Core --filter "MethodName=Redis_Helper_Should_Work"
```

## Test Organization

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test component interactions
- **Example Tests**: Demonstrate test infrastructure usage

## Dependencies

The test project references:
- `SmaTrendFollower.Console` - Main application project
- Standard testing packages (xUnit, FluentAssertions, etc.)

## Migration from Legacy Tests

The legacy test project (`SmaTrendFollower.Tests`) has been archived. Key differences:

1. **Cleaner Dependencies**: Only essential testing packages
2. **Better Helpers**: Improved Redis and HTTP mocking utilities
3. **Modern Patterns**: Uses latest testing best practices
4. **Faster Execution**: Optimized for performance

## CI/CD Integration

The CI pipeline (`../.github/workflows/build.yml`) has been updated to use this test project:

```yaml
- run: dotnet test SmaTrendFollower.Tests.Core/SmaTrendFollower.Tests.Core.csproj --configuration Release --collect:"XPlat Code Coverage"
```

## Notes

- Redis tests require a Redis server running on `localhost:6379`
- HTTP mocking tests work without external dependencies
- All test helpers include proper disposal patterns
- Tests use async/await patterns throughout
